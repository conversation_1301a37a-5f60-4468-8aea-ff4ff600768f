"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/status/page",{

/***/ "(app-pages-browser)/./src/app/warranties/status/page.tsx":
/*!********************************************!*\
  !*** ./src/app/warranties/status/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WarrantyStatusPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Calendar,CheckCircle,RefreshCw,Shield,TrendingDown,TrendingUp,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n// import { Progress } from '@/components/ui/progress'; // TODO: Create Progress component\n\n\n\n\n/**\n * Warranty Status Dashboard Page\n * \n * This page provides a comprehensive overview of warranty status across\n * all products, with visual indicators and key metrics.\n */ function WarrantyStatusPage() {\n    _s();\n    _s1();\n    const [statusData, setStatusData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock data for development\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WarrantyStatusPage.useEffect\": ()=>{\n            loadStatusData();\n        }\n    }[\"WarrantyStatusPage.useEffect\"], []);\n    const loadStatusData = async ()=>{\n        try {\n            setIsLoading(true);\n            // TODO: Replace with actual API call\n            // const response = await fetch('/api/warranties/status');\n            // const data = await response.json();\n            // Mock data for now\n            const mockData = {\n                summary: {\n                    total: 150,\n                    active: 120,\n                    expiring: 15,\n                    expired: 10,\n                    pending: 5\n                },\n                trends: {\n                    activeChange: 5,\n                    expiringChange: -2,\n                    expiredChange: 3\n                },\n                expiringBreakdown: {\n                    next7Days: 3,\n                    next30Days: 12,\n                    next90Days: 25\n                },\n                componentStatus: {\n                    total: 450,\n                    active: 380,\n                    expiring: 35,\n                    expired: 35\n                },\n                recentAlerts: [\n                    {\n                        id: 1,\n                        type: 'expiring',\n                        message: 'ABC Corporation warranty expires in 5 days',\n                        date: '2024-01-20',\n                        priority: 'high'\n                    },\n                    {\n                        id: 2,\n                        type: 'expired',\n                        message: 'XYZ Industries warranty has expired',\n                        date: '2024-01-19',\n                        priority: 'critical'\n                    }\n                ]\n            };\n            setStatusData(mockData);\n            setError(null);\n        } catch (err) {\n            console.error('Error loading status data:', err);\n            setError('Failed to load warranty status data');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleRefresh = async ()=>{\n        setIsRefreshing(true);\n        await loadStatusData();\n        setIsRefreshing(false);\n    };\n    const getStatusPercentage = (count, total)=>{\n        return total > 0 ? Math.round(count / total * 100) : 0;\n    };\n    const getTrendIcon = (change)=>{\n        if (change > 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-4 w-4 text-green-600\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 95,\n            columnNumber: 28\n        }, this);\n        if (change < 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-4 w-4 text-red-600\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 96,\n            columnNumber: 28\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 97,\n            columnNumber: 12\n        }, this);\n    };\n    const getPriorityBadge = (priority)=>{\n        switch(priority){\n            case 'critical':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"destructive\",\n                    children: \"Critical\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 16\n                }, this);\n            case 'high':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    className: \"bg-orange-100 text-orange-800\",\n                    children: \"High\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 16\n                }, this);\n            case 'medium':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"Medium\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    variant: \"secondary\",\n                    children: \"Low\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                className: \"h-8 w-64\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                className: \"h-4 w-96\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: Array.from({\n                                length: 4\n                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                className: \"h-12 w-12 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                className: \"h-8 w-16 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                className: \"h-4 w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 19\n                                    }, this)\n                                }, i, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 30\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 112,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Warranty Status Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-gray-100\",\n                                    children: \"Monitor warranty status and expiration alerts across all products\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"secondary\",\n                                    onClick: handleRefresh,\n                                    disabled: isRefreshing,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(isRefreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"secondary\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                        href: \"/warranties/alerts\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View Alerts\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                    className: \"text-black\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 21\n                        }, this),\n                        statusData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Total Warranties\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: statusData.summary.total\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-8 w-8 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: statusData.summary.active\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 mt-1\",\n                                                                    children: [\n                                                                        getTrendIcon(statusData.trends.activeChange),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                Math.abs(statusData.trends.activeChange),\n                                                                                \" this month\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 188,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-8 w-8 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Expiring Soon\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: statusData.summary.expiring\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 mt-1\",\n                                                                    children: [\n                                                                        getTrendIcon(statusData.trends.expiringChange),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                Math.abs(statusData.trends.expiringChange),\n                                                                                \" this month\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 206,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-8 w-8 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600\",\n                                                                    children: \"Expired\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: statusData.summary.expired\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 mt-1\",\n                                                                    children: [\n                                                                        getTrendIcon(statusData.trends.expiredChange),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                Math.abs(statusData.trends.expiredChange),\n                                                                                \" this month\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 224,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 text-red-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-black\",\n                                                        children: \"Warranty Status Distribution\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Active\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 245,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    getStatusPercentage(statusData.summary.active, statusData.summary.total),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 246,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-green-600 h-2 rounded-full\",\n                                                                            style: {\n                                                                                width: \"\".concat(getStatusPercentage(statusData.summary.active, statusData.summary.total), \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 251,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Expiring Soon\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    getStatusPercentage(statusData.summary.expiring, statusData.summary.total),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 260,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-yellow-600 h-2 rounded-full\",\n                                                                            style: {\n                                                                                width: \"\".concat(getStatusPercentage(statusData.summary.expiring, statusData.summary.total), \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Expired\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 273,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    getStatusPercentage(statusData.summary.expired, statusData.summary.total),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 274,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                                                        value: getStatusPercentage(statusData.summary.expired, statusData.summary.total),\n                                                                        className: \"h-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"text-black\",\n                                                        children: \"Expiration Timeline\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-red-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-red-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Next 7 Days\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 293,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"destructive\",\n                                                                        children: statusData.expiringBreakdown.next7Days\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 295,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-yellow-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Next 30 Days\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 301,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"bg-yellow-100 text-yellow-800\",\n                                                                        children: statusData.expiringBreakdown.next30Days\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-blue-50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 310,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: \"Next 90 Days\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"bg-blue-100 text-blue-800\",\n                                                                        children: statusData.expiringBreakdown.next90Days\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-black\",\n                                                    children: \"Recent Alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-black\",\n                                                    children: \"Latest warranty expiration notifications and alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: statusData.recentAlerts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-8 w-8 mx-auto text-gray-400 mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"No recent alerts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 59\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: statusData.recentAlerts.map((alert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    alert.type === 'expiring' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 58\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Calendar_CheckCircle_RefreshCw_Shield_TrendingDown_TrendingUp_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 114\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm font-medium text-black\",\n                                                                                children: alert.message\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: alert.date\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            getPriorityBadge(alert.priority)\n                                                        ]\n                                                    }, alert.id, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 61\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 30\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\status\\\\page.tsx\",\n        lineNumber: 134,\n        columnNumber: 10\n    }, this);\n}\n_s(WarrantyStatusPage, \"z8S2GUJE7HR5JfwCno5EwJiU7G8=\");\n_c1 = WarrantyStatusPage;\n_s1(WarrantyStatusPage, \"z8S2GUJE7HR5JfwCno5EwJiU7G8=\");\n_c = WarrantyStatusPage;\nvar _c;\n$RefreshReg$(_c, \"WarrantyStatusPage\");\nvar _c1;\n$RefreshReg$(_c1, \"WarrantyStatusPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/status/page.tsx\n"));

/***/ })

});